/**
 * API 配置文件
 * 统一管理 API 基础配置和接口定义
 */

// 导入配置模块
const {
  ENVIRONMENTS,
  getCurrentEnvironment,
  getApiConfig,
  getApiBaseUrl,
  getApiTimeout,
  getApiRetryTimes
} = require('./env');

// 当前环境
const CURRENT_ENV = getCurrentEnvironment();

// 获取当前环境的 API 配置
const config = getApiConfig();

// API 端点配置
const API_ENDPOINTS = {
  // 基础路径
  BASE: '/api',
  
  // 品牌相关接口
  BRANDS: {
    LIST: '/api/brands',
    DETAIL: (id) => `/api/brands/${id}`,
    CREATE: '/api/brands',
    UPDATE: (id) => `/api/brands/${id}`,
    DELETE: (id) => `/api/brands/${id}`
  },
  
  // 用户相关接口
  USERS: {
    LIST: '/api/users',
    DETAIL: (id) => `/api/users/${id}`,
    CREATE: '/api/users',
    UPDATE: (id) => `/api/users/${id}`,
    DELETE: (id) => `/api/users/${id}`,
    AUTH: '/api/users/auth',
    REGENERATE_AUTH: (id) => `/api/users/${id}/regenerate-auth`
  },
  
  // 系统信息接口
  SYSTEM: {
    INFO: '/',
    DOCS: '/api-docs'
  }
};

// HTTP 状态码
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
};

// 请求头配置
const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
};

module.exports = {
  // 环境信息
  CURRENT_ENV,
  config,

  // API 端点和状态码
  API_ENDPOINTS,
  HTTP_STATUS,
  DEFAULT_HEADERS,

  // 辅助函数
  getBaseUrl: () => config.BASE_URL,
  getTimeout: () => config.TIMEOUT,
  getRetryTimes: () => config.RETRY_TIMES,
  getConfig: () => config,

  // 构建完整的 API URL
  buildUrl: (endpoint) => {
    const baseUrl = config.BASE_URL.replace(/\/$/, ''); // 移除末尾斜杠
    const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${baseUrl}${path}`;
  },

  // 环境检查函数（从 env.js 重新导出）
  isDevelopment: () => getCurrentEnvironment() === 'development',
  isProduction: () => getCurrentEnvironment() === 'production',
  isTest: () => getCurrentEnvironment() === 'test'
};
