directories:
  output: build-output
  buildResources: build
appId: com.xiaolai.aitools
productName: AI重器
files:
  - filter:
      - src/**/*
      - node_modules/**/*
      - package.json
extraResources:
  - from: assets
    to: assets
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
  icon: assets/icon.ico
  publisherName: xiaolai
mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
  icon: assets/icon.icns
  category: public.app-category.productivity
linux:
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
  icon: assets/icon.png
  category: Utility
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
publish:
  provider: github
  owner: laixiao
  repo: AiTools
electronVersion: 28.3.3
