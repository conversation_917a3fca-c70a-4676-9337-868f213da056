# 📱 应用图标创建指南

## 🎨 图标要求

### 基本要求
- **尺寸**: 512x512 像素
- **格式**: PNG（原始图片）
- **背景**: 透明或纯色
- **风格**: 简洁、清晰、易识别

### 平台特定格式
- **Windows**: icon.ico（包含16x16, 32x32, 48x48, 256x256等多种尺寸）
- **macOS**: icon.icns（包含16x16到1024x1024的多种尺寸）
- **Linux**: icon.png（512x512像素）

## 🛠️ 创建步骤

### 1. 设计原始图标
使用以下工具之一创建 512x512 的 PNG 图标：
- [Canva](https://www.canva.com/) - 在线设计工具
- [GIMP](https://www.gimp.org/) - 免费图像编辑器
- [Figma](https://www.figma.com/) - 在线设计工具
- Adobe Photoshop/Illustrator

### 2. 转换为不同格式

#### 在线转换工具（推荐）
- **PNG to ICO**: https://convertio.co/png-ico/
- **PNG to ICNS**: https://convertio.co/png-icns/
- **多格式转换**: https://cloudconvert.com/

#### 本地工具
- **ImageMagick** (命令行)
  ```bash
  # 转换为 ICO
  magick icon.png -define icon:auto-resize=256,128,64,48,32,16 icon.ico
  
  # 转换为 ICNS (需要在 macOS 上)
  iconutil -c icns icon.iconset
  ```

### 3. 文件放置
将生成的图标文件放置在 `assets` 文件夹中：
```
assets/
├── icon.png    # 512x512 PNG (Linux)
├── icon.ico    # 多尺寸 ICO (Windows)
└── icon.icns   # 多尺寸 ICNS (macOS)
```

## 🎯 设计建议

### 图标设计原则
1. **简洁明了**: 避免过于复杂的细节
2. **高对比度**: 确保在不同背景下都清晰可见
3. **可缩放**: 在小尺寸下仍然清晰
4. **品牌一致**: 与应用主题保持一致

### 颜色建议
- 使用2-3种主要颜色
- 避免使用过多渐变
- 考虑深色和浅色主题的兼容性

### 形状建议
- 圆角矩形或圆形效果较好
- 避免过于尖锐的边角
- 保持适当的内边距

## 📝 示例图标创建

如果你没有设计经验，可以：

1. **使用 Emoji**
   - 选择一个相关的 emoji
   - 在 512x512 的画布上放大
   - 添加简单的背景色

2. **使用字母**
   - 应用名称的首字母
   - 选择好看的字体
   - 添加背景色和边框

3. **使用简单图形**
   - 几何形状组合
   - 简单的线条图标
   - 扁平化设计风格

## 🔧 验证图标

创建完成后，验证图标是否正确：

1. **检查文件**
   ```bash
   ls -la assets/
   # 应该看到三个图标文件
   ```

2. **测试构建**
   ```bash
   npm run build
   # 检查是否有图标相关错误
   ```

3. **查看效果**
   - 在不同操作系统上安装应用
   - 检查桌面快捷方式图标
   - 检查任务栏/Dock 中的图标

## 🎨 免费图标资源

如果需要灵感或素材：
- [Feather Icons](https://feathericons.com/) - 简洁线条图标
- [Heroicons](https://heroicons.com/) - 现代图标集
- [Tabler Icons](https://tabler-icons.io/) - 免费图标库
- [Iconify](https://iconify.design/) - 大量免费图标

记住要检查使用许可，确保可以用于你的项目！
