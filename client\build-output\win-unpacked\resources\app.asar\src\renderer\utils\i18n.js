/**
 * 国际化工具函数
 * 统一处理多语言相关功能
 */

/**
 * 获取嵌套对象的值
 * @param {object} obj - 对象
 * @param {string} key - 嵌套键，用点分隔
 * @returns {any} 值
 */
function getNestedValue(obj, key) {
  if (!obj || !key) return undefined;
  
  return key.split('.').reduce((current, keyPart) => {
    return current && current[keyPart] !== undefined ? current[keyPart] : undefined;
  }, obj);
}

/**
 * 设置嵌套对象的值
 * @param {object} obj - 对象
 * @param {string} key - 嵌套键，用点分隔
 * @param {any} value - 值
 */
function setNestedValue(obj, key, value) {
  if (!obj || !key) return;
  
  const keys = key.split('.');
  const lastKey = keys.pop();
  
  const target = keys.reduce((current, keyPart) => {
    if (!current[keyPart]) {
      current[keyPart] = {};
    }
    return current[keyPart];
  }, obj);
  
  target[lastKey] = value;
}

/**
 * 加载语言文件
 * @param {string} language - 语言代码
 * @returns {Promise<object>} 语言数据
 */
async function loadLanguageFile(language) {
  try {
    const fs = require('fs');
    const path = require('path');
    
    const filePath = path.join(__dirname, '../assets/locales', `${language}.json`);
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`加载语言文件失败: ${language}`, error);
    return {};
  }
}

/**
 * 格式化文本（支持参数替换）
 * @param {string} template - 模板文本
 * @param {object} params - 参数对象
 * @returns {string} 格式化后的文本
 */
function formatText(template, params = {}) {
  if (!template) return '';
  
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return params[key] !== undefined ? params[key] : match;
  });
}

/**
 * 更新页面中的国际化文本
 * @param {object} i18nTexts - 国际化文本对象
 */
function updateI18nTexts(i18nTexts) {
  // 更新带有 data-i18n 属性的元素
  document.querySelectorAll('[data-i18n]').forEach(element => {
    const key = element.getAttribute('data-i18n');
    const text = getNestedValue(i18nTexts, key);
    if (text) {
      element.textContent = text;
    }
  });
  
  // 更新带有 data-i18n-placeholder 属性的元素
  document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
    const key = element.getAttribute('data-i18n-placeholder');
    const text = getNestedValue(i18nTexts, key);
    if (text) {
      element.placeholder = text;
    }
  });
  
  // 更新带有 data-i18n-title 属性的元素
  document.querySelectorAll('[data-i18n-title]').forEach(element => {
    const key = element.getAttribute('data-i18n-title');
    const text = getNestedValue(i18nTexts, key);
    if (text) {
      element.title = text;
    }
  });
}

/**
 * 获取浏览器语言
 * @returns {string} 语言代码
 */
function getBrowserLanguage() {
  const language = navigator.language || navigator.userLanguage;
  
  // 将语言代码标准化
  if (language.startsWith('zh')) {
    return 'zh-CN';
  } else if (language.startsWith('en')) {
    return 'en-US';
  }
  
  return 'zh-CN'; // 默认中文
}

/**
 * 验证语言代码
 * @param {string} language - 语言代码
 * @returns {boolean} 是否有效
 */
function isValidLanguage(language) {
  const supportedLanguages = ['zh-CN', 'en-US'];
  return supportedLanguages.includes(language);
}

/**
 * 获取语言显示名称
 * @param {string} language - 语言代码
 * @returns {string} 显示名称
 */
function getLanguageDisplayName(language) {
  const displayNames = {
    'zh-CN': '中文 (简体)',
    'en-US': 'English'
  };
  
  return displayNames[language] || language;
}

/**
 * 切换语言
 * @param {string} newLanguage - 新语言代码
 * @param {object} currentTexts - 当前语言文本
 * @returns {Promise<object>} 新语言文本
 */
async function switchLanguage(newLanguage, currentTexts = {}) {
  if (!isValidLanguage(newLanguage)) {
    console.warn(`不支持的语言: ${newLanguage}`);
    return currentTexts;
  }
  
  try {
    const newTexts = await loadLanguageFile(newLanguage);
    updateI18nTexts(newTexts);
    
    // 通知主进程语言已更改
    try {
      const { ipcRenderer } = require('electron');
      ipcRenderer.invoke('set-setting', 'language', newLanguage);
    } catch (error) {
      console.warn('无法通知主进程语言更改:', error);
    }
    
    return newTexts;
  } catch (error) {
    console.error('切换语言失败:', error);
    return currentTexts;
  }
}

/**
 * 创建国际化文本获取函数
 * @param {object} i18nTexts - 国际化文本对象
 * @returns {function} 文本获取函数
 */
function createTextGetter(i18nTexts) {
  return function t(key, params = {}, fallback = '') {
    const text = getNestedValue(i18nTexts, key) || fallback;
    return formatText(text, params);
  };
}

module.exports = {
  getNestedValue,
  setNestedValue,
  loadLanguageFile,
  formatText,
  updateI18nTexts,
  getBrowserLanguage,
  isValidLanguage,
  getLanguageDisplayName,
  switchLanguage,
  createTextGetter
};
