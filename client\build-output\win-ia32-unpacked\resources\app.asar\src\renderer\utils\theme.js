/**
 * 主题工具函数
 * 统一处理主题相关功能
 */

/**
 * 支持的主题列表
 */
const THEMES = {
  AUTO: 'auto',
  LIGHT: 'light',
  DARK: 'dark'
};

/**
 * 主题显示名称
 */
const THEME_NAMES = {
  [THEMES.AUTO]: '跟随系统',
  [THEMES.LIGHT]: '浅色',
  [THEMES.DARK]: '深色'
};

/**
 * 应用主题
 * @param {string} theme - 主题名称
 */
function applyTheme(theme) {
  if (!isValidTheme(theme)) {
    console.warn(`不支持的主题: ${theme}`);
    return;
  }
  
  // 设置 data-theme 属性
  document.documentElement.setAttribute('data-theme', theme);
  
  // 如果是自动主题，检查系统偏好
  if (theme === THEMES.AUTO) {
    handleAutoTheme();
  }
  
  console.log(`主题已应用: ${theme}`);
}

/**
 * 处理自动主题
 */
function handleAutoTheme() {
  // 检查系统是否支持深色模式
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    // 根据系统偏好设置主题
    updateAutoTheme(mediaQuery.matches);
    
    // 监听系统主题变化
    mediaQuery.addEventListener('change', (e) => {
      updateAutoTheme(e.matches);
    });
  }
}

/**
 * 更新自动主题
 * @param {boolean} isDark - 是否为深色模式
 */
function updateAutoTheme(isDark) {
  const currentTheme = document.documentElement.getAttribute('data-theme');
  
  // 只有在自动主题模式下才更新
  if (currentTheme === THEMES.AUTO) {
    document.documentElement.setAttribute('data-theme', isDark ? THEMES.DARK : THEMES.LIGHT);
  }
}

/**
 * 获取当前主题
 * @returns {string} 当前主题
 */
function getCurrentTheme() {
  return document.documentElement.getAttribute('data-theme') || THEMES.AUTO;
}

/**
 * 验证主题是否有效
 * @param {string} theme - 主题名称
 * @returns {boolean} 是否有效
 */
function isValidTheme(theme) {
  return Object.values(THEMES).includes(theme);
}

/**
 * 获取主题显示名称
 * @param {string} theme - 主题名称
 * @returns {string} 显示名称
 */
function getThemeDisplayName(theme) {
  return THEME_NAMES[theme] || theme;
}

/**
 * 获取所有可用主题
 * @returns {Array} 主题列表
 */
function getAvailableThemes() {
  return Object.values(THEMES).map(theme => ({
    value: theme,
    label: getThemeDisplayName(theme)
  }));
}

/**
 * 切换主题
 * @param {string} newTheme - 新主题
 * @returns {Promise} 切换结果
 */
async function switchTheme(newTheme) {
  if (!isValidTheme(newTheme)) {
    throw new Error(`不支持的主题: ${newTheme}`);
  }
  
  try {
    // 应用主题
    applyTheme(newTheme);
    
    // 保存主题设置
    try {
      const { ipcRenderer } = require('electron');
      await ipcRenderer.invoke('set-setting', 'theme', newTheme);

      // 通知主进程主题已更改
      await ipcRenderer.invoke('apply-theme', newTheme);
    } catch (error) {
      console.warn('无法通知主进程主题更改:', error);
    }
    
    return { success: true, theme: newTheme };
  } catch (error) {
    console.error('切换主题失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 加载并应用保存的主题
 * @returns {Promise<string>} 加载的主题
 */
async function loadAndApplyTheme() {
  try {
    let theme = THEMES.AUTO;
    
    // 从设置中加载主题
    try {
      const { ipcRenderer } = require('electron');
      theme = await ipcRenderer.invoke('get-setting', 'theme') || THEMES.AUTO;
    } catch (error) {
      console.warn('无法从主进程获取主题设置:', error);
    }
    
    // 应用主题
    applyTheme(theme);
    
    return theme;
  } catch (error) {
    console.error('加载主题失败:', error);
    applyTheme(THEMES.AUTO);
    return THEMES.AUTO;
  }
}

/**
 * 检测系统主题偏好
 * @returns {string} 系统偏好的主题
 */
function detectSystemTheme() {
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    return THEMES.DARK;
  }
  return THEMES.LIGHT;
}

/**
 * 获取主题相关的 CSS 变量
 * @param {string} theme - 主题名称
 * @returns {object} CSS 变量对象
 */
function getThemeVariables(theme) {
  const root = document.documentElement;
  const computedStyle = getComputedStyle(root);
  
  // 获取主题相关的 CSS 变量
  const variables = {};
  const themeVars = [
    '--bg-primary',
    '--bg-secondary',
    '--bg-tertiary',
    '--bg-card',
    '--text-primary',
    '--text-secondary',
    '--text-muted',
    '--border-color',
    '--shadow-color',
    '--input-bg',
    '--button-primary',
    '--button-hover',
    '--alert-bg'
  ];
  
  themeVars.forEach(varName => {
    variables[varName] = computedStyle.getPropertyValue(varName).trim();
  });
  
  return variables;
}

/**
 * 监听主题变化事件
 * @param {function} callback - 回调函数
 */
function onThemeChange(callback) {
  if (typeof callback !== 'function') {
    console.warn('主题变化回调必须是函数');
    return;
  }
  
  // 监听 IPC 主题变化事件
  try {
    const { ipcRenderer } = require('electron');
    ipcRenderer.on('theme-changed', (event, theme) => {
      applyTheme(theme);
      callback(theme);
    });
  } catch (error) {
    console.warn('无法监听主进程主题变化事件:', error);
  }
  
  // 监听系统主题变化
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', (e) => {
      const currentTheme = getCurrentTheme();
      if (currentTheme === THEMES.AUTO) {
        callback(currentTheme);
      }
    });
  }
}

/**
 * 创建主题切换器
 * @param {HTMLElement} container - 容器元素
 * @param {object} options - 选项
 */
function createThemeSwitcher(container, options = {}) {
  const {
    showLabels = true,
    className = 'theme-switcher',
    onChange = null
  } = options;
  
  const switcher = document.createElement('div');
  switcher.className = className;
  
  const themes = getAvailableThemes();
  const currentTheme = getCurrentTheme();
  
  themes.forEach(({ value, label }) => {
    const button = document.createElement('button');
    button.textContent = showLabels ? label : '';
    button.value = value;
    button.className = value === currentTheme ? 'active' : '';
    
    button.addEventListener('click', async () => {
      const result = await switchTheme(value);
      if (result.success) {
        // 更新按钮状态
        switcher.querySelectorAll('button').forEach(btn => {
          btn.className = btn.value === value ? 'active' : '';
        });
        
        // 调用回调
        if (onChange) {
          onChange(value);
        }
      }
    });
    
    switcher.appendChild(button);
  });
  
  container.appendChild(switcher);
  return switcher;
}

module.exports = {
  THEMES,
  THEME_NAMES,
  applyTheme,
  getCurrentTheme,
  isValidTheme,
  getThemeDisplayName,
  getAvailableThemes,
  switchTheme,
  loadAndApplyTheme,
  detectSystemTheme,
  getThemeVariables,
  onThemeChange,
  createThemeSwitcher
};
