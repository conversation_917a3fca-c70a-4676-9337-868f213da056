
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { margin: 0; padding: 20px; background: #f0f0f0; }
        .container { text-align: center; }
        .icon { width: 512px; height: 512px; border: 1px solid #ccc; }
        .instructions { margin-top: 20px; max-width: 600px; text-align: left; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Electron App 示例图标</h1>
        <div class="icon">
            <svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect x="32" y="32" width="448" height="448" rx="80" ry="80" fill="url(#grad1)" />
  
  <!-- 主图标 - 电子符号 -->
  <circle cx="256" cy="200" r="20" fill="white" opacity="0.9"/>
  
  <!-- 电子轨道 -->
  <ellipse cx="256" cy="256" rx="120" ry="40" fill="none" stroke="white" stroke-width="8" opacity="0.7"/>
  <ellipse cx="256" cy="256" rx="40" ry="120" fill="none" stroke="white" stroke-width="8" opacity="0.7"/>
  <ellipse cx="256" cy="256" rx="85" ry="85" fill="none" stroke="white" stroke-width="6" opacity="0.5"/>
  
  <!-- 中心核心 -->
  <circle cx="256" cy="256" r="16" fill="white"/>
  
  <!-- 装饰性文字 -->
  <text x="256" y="380" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="white" opacity="0.8">E</text>
</svg>
        </div>
        
        <div class="instructions">
            <h2>如何使用这个图标：</h2>
            <ol>
                <li><strong>保存为PNG：</strong>
                    <ul>
                        <li>右键点击上面的图标</li>
                        <li>选择"另存为图片"或"复制图片"</li>
                        <li>保存为 <code>icon.png</code> 到 <code>assets</code> 文件夹</li>
                    </ul>
                </li>
                <li><strong>转换为其他格式：</strong>
                    <ul>
                        <li>访问 <a href="https://convertio.co/png-ico/" target="_blank">PNG to ICO 转换器</a></li>
                        <li>访问 <a href="https://convertio.co/png-icns/" target="_blank">PNG to ICNS 转换器</a></li>
                        <li>上传你的 PNG 文件并下载转换后的文件</li>
                    </ul>
                </li>
                <li><strong>放置文件：</strong>
                    <ul>
                        <li><code>assets/icon.png</code> - Linux 图标</li>
                        <li><code>assets/icon.ico</code> - Windows 图标</li>
                        <li><code>assets/icon.icns</code> - macOS 图标</li>
                    </ul>
                </li>
            </ol>
            
            <h2>自定义图标：</h2>
            <p>你可以：</p>
            <ul>
                <li>使用 <a href="https://www.canva.com/" target="_blank">Canva</a> 等在线工具设计自己的图标</li>
                <li>修改上面的 SVG 代码来改变颜色和样式</li>
                <li>使用专业的图标设计软件如 Adobe Illustrator</li>
            </ul>
            
            <h2>设计建议：</h2>
            <ul>
                <li>保持简洁，避免过多细节</li>
                <li>使用高对比度的颜色</li>
                <li>确保在小尺寸下仍然清晰</li>
                <li>考虑不同操作系统的设计风格</li>
            </ul>
        </div>
    </div>
</body>
</html>
