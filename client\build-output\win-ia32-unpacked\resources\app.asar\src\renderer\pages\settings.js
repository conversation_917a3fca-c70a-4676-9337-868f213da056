const { ipc<PERSON>enderer } = require('electron');

// 设置项的默认值
const DEFAULT_SETTINGS = {
    language: 'zh-CN',
    autoStart: false,
    minimizeToTray: false,
    autoUpdate: true,
    theme: 'auto',
    updateChannel: 'github',
    customUpdateUrl: ''
};

// 当前设置
let currentSettings = { ...DEFAULT_SETTINGS };

// 国际化文本
let i18nTexts = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    await loadI18nTexts();
    await loadSettings();
    await loadAppInfo();
    await loadUserInfo();
    setupEventListeners();
    updateUI();

    // 应用当前主题
    applyTheme(currentSettings.theme || 'auto');
});

// 监听语言更改事件
ipcRenderer.on('language-changed', async (event, newLanguage) => {
    console.log('设置页面收到语言更改通知:', newLanguage);
    await loadI18nTexts();
    // 更新当前设置中的语言
    currentSettings.language = newLanguage;
    updateUI();
});

// 监听主题更改事件
ipcRenderer.on('theme-changed', (event, theme) => {
    console.log('设置页面收到主题更改通知:', theme);
    applyTheme(theme);
});

// 应用主题
function applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    console.log('主题已应用:', theme);
}

// 加载国际化文本
async function loadI18nTexts() {
    try {
        const language = await ipcRenderer.invoke('get-setting', 'language') || 'zh-CN';
        i18nTexts = await ipcRenderer.invoke('get-i18n-texts', language);
        updateI18nTexts();
    } catch (error) {
        console.error('加载国际化文本失败:', error);
        // 使用默认中文文本
        i18nTexts = await getDefaultI18nTexts('zh-CN');
        updateI18nTexts();
    }
}

// 获取默认国际化文本
async function getDefaultI18nTexts(language) {
    const texts = {
        'zh-CN': {
            'settings.title': '设置',
            'common.back': '返回',
            'settings.language.title': '语言设置',
            'settings.language.interface': '界面语言',
            'settings.language.description': '选择应用程序的显示语言',
            'settings.language.chinese': '中文 (简体)',
            'settings.language.english': 'English',
            'settings.language.preview': '语言预览：当前选择的语言将立即应用到界面',
            'settings.app.title': '应用设置',
            'settings.app.startup': '开机自启动',
            'settings.app.startup_description': '系统启动时自动运行应用',
            'settings.app.minimize_to_tray': '最小化到系统托盘',
            'settings.app.minimize_description': '关闭窗口时最小化到系统托盘而不是退出',
            'settings.app.auto_update': '自动更新',
            'settings.app.update_description': '自动检查并下载应用更新',
            'settings.appearance.title': '外观设置',
            'settings.appearance.theme': '主题',
            'settings.appearance.theme_description': '选择应用程序的外观主题',
            'settings.appearance.auto': '跟随系统',
            'settings.appearance.light': '浅色',
            'settings.appearance.dark': '深色',
            'settings.about.title': '关于',
            'settings.about.version': '应用版本',
            'settings.about.version_description': '当前应用程序版本信息',
            'settings.about.build_date': '构建日期: 2024-01-01',
            'settings.actions.reset': '重置设置',
            'settings.actions.save': '保存设置',
            'settings.messages.saved': '设置已保存',
            'settings.messages.reset': '设置已重置为默认值',
            'settings.messages.language_changed': '语言已更改，重启应用后生效'
        },
        'en-US': {
            'settings.title': 'Settings',
            'common.back': 'Back',
            'settings.language.title': 'Language Settings',
            'settings.language.interface': 'Interface Language',
            'settings.language.description': 'Select the display language for the application',
            'settings.language.chinese': '中文 (简体)',
            'settings.language.english': 'English',
            'settings.language.preview': 'Language Preview: The selected language will be applied to the interface immediately',
            'settings.app.title': 'Application Settings',
            'settings.app.startup': 'Auto Start',
            'settings.app.startup_description': 'Automatically run the application when the system starts',
            'settings.app.minimize_to_tray': 'Minimize to System Tray',
            'settings.app.minimize_description': 'Minimize to system tray instead of exiting when closing the window',
            'settings.app.auto_update': 'Auto Update',
            'settings.app.update_description': 'Automatically check and download application updates',
            'settings.appearance.title': 'Appearance Settings',
            'settings.appearance.theme': 'Theme',
            'settings.appearance.theme_description': 'Select the appearance theme for the application',
            'settings.appearance.auto': 'Follow System',
            'settings.appearance.light': 'Light',
            'settings.appearance.dark': 'Dark',
            'settings.about.title': 'About',
            'settings.about.version': 'Application Version',
            'settings.about.version_description': 'Current application version information',
            'settings.about.build_date': 'Build Date: 2024-01-01',
            'settings.actions.reset': 'Reset Settings',
            'settings.actions.save': 'Save Settings',
            'settings.messages.saved': 'Settings saved',
            'settings.messages.reset': 'Settings reset to default values',
            'settings.messages.language_changed': 'Language changed, restart the application to take effect'
        }
    };
    return texts[language] || texts['zh-CN'];
}

// 更新界面文本
function updateI18nTexts() {
    document.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        const text = getNestedValue(i18nTexts, key);
        if (text) {
            if (element.tagName === 'INPUT' && element.type === 'text') {
                element.placeholder = text;
            } else {
                element.textContent = text;
            }
        }
    });
}

// 获取嵌套对象的值
function getNestedValue(obj, key) {
    return key.split('.').reduce((current, keyPart) => {
        return current && current[keyPart] !== undefined ? current[keyPart] : undefined;
    }, obj);
}

// 加载设置
async function loadSettings() {
    try {
        currentSettings = await ipcRenderer.invoke('get-all-settings') || { ...DEFAULT_SETTINGS };
    } catch (error) {
        console.error('加载设置失败:', error);
        currentSettings = { ...DEFAULT_SETTINGS };
    }
}

// 加载应用信息
async function loadAppInfo() {
    try {
        // 获取版本
        const version = await ipcRenderer.invoke('get-app-version');
        document.getElementById('appVersion').textContent = `v${version}`;

        // 获取构建日期
        const buildDate = await ipcRenderer.invoke('get-build-date');
        const buildDateElement = document.querySelector('[data-i18n="settings.about.build_date"]');
        if (buildDateElement) {
            const buildDateText = getNestedValue(i18nTexts, 'settings.about.build_date') || '构建日期: {date}';
            buildDateElement.textContent = buildDateText.replace('{date}', buildDate);
        }
    } catch (error) {
        console.error('获取应用信息失败:', error);
    }
}

// 加载用户信息
async function loadUserInfo() {
    try {
        const user = await ipcRenderer.invoke('get-current-user');
        if (user) {
            const userInfoElement = document.getElementById('userDisplayInfo');
            const usernameText = getNestedValue(i18nTexts, 'main.user_info.username') || '用户名: {username}';
            const typeText = getNestedValue(i18nTexts, 'main.user_info.type') || '类型: {userType}';
            const statusText = getNestedValue(i18nTexts, 'main.user_info.status') || '状态: {status}';
            const activeText = getNestedValue(i18nTexts, 'main.user_info.active') || '活跃';
            const inactiveText = getNestedValue(i18nTexts, 'main.user_info.inactive') || '禁用';

            userInfoElement.innerHTML = `
                ${usernameText.replace('{username}', user.displayName || user.username)}<br>
                ${typeText.replace('{userType}', user.userTypeDisplay)}<br>
                ${statusText.replace('{status}', user.isActive ? activeText : inactiveText)}
            `;
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
        const userInfoElement = document.getElementById('userDisplayInfo');
        userInfoElement.textContent = getNestedValue(i18nTexts, 'admin.login_required') || '请先登录';
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 语言选择
    const languageSelect = document.getElementById('languageSelect');
    languageSelect.addEventListener('change', async function() {
        const newLanguage = this.value;
        currentSettings.language = newLanguage;

        // 立即保存设置
        try {
            // 先保存设置到主进程
            const saveResult = await ipcRenderer.invoke('save-all-settings', currentSettings);

            if (saveResult.success) {
                // 重新获取更新后的国际化文本
                i18nTexts = await ipcRenderer.invoke('get-i18n-texts', newLanguage);
                updateI18nTexts();

                showStatus(getNestedValue(i18nTexts, 'settings.messages.language_changed') || '语言已更改，切换页面时生效', 'success');
            } else {
                showStatus('保存设置失败: ' + saveResult.error, 'danger');
            }
        } catch (error) {
            console.error('保存语言设置失败:', error);
            showStatus('保存语言设置失败: ' + error.message, 'danger');
        }
    });

    // 其他设置项
    document.getElementById('autoStartSwitch').addEventListener('change', function() {
        currentSettings.autoStart = this.checked;
    });

    document.getElementById('minimizeToTraySwitch').addEventListener('change', function() {
        currentSettings.minimizeToTray = this.checked;
    });

    document.getElementById('autoUpdateSwitch').addEventListener('change', function() {
        currentSettings.autoUpdate = this.checked;
    });

    document.getElementById('themeSelect').addEventListener('change', async function() {
        currentSettings.theme = this.value;

        // 立即应用主题
        try {
            await ipcRenderer.invoke('apply-theme', this.value);
            applyTheme(this.value);
            showStatus(getNestedValue(i18nTexts, 'settings.messages.theme_changed') || '主题已更改', 'success');
        } catch (error) {
            console.error('应用主题失败:', error);
            showStatus('应用主题失败: ' + error.message, 'danger');
        }
    });

    // 更新通道选择
    document.getElementById('updateChannelSelect').addEventListener('change', function() {
        currentSettings.updateChannel = this.value;
        toggleCustomUrlSetting();
    });

    // 自定义更新地址
    document.getElementById('customUpdateUrl').addEventListener('change', function() {
        currentSettings.customUpdateUrl = this.value;
    });
}

// 更新UI
function updateUI() {
    // 更新语言选择
    document.getElementById('languageSelect').value = currentSettings.language;

    // 更新开关状态
    document.getElementById('autoStartSwitch').checked = currentSettings.autoStart;
    document.getElementById('minimizeToTraySwitch').checked = currentSettings.minimizeToTray;
    document.getElementById('autoUpdateSwitch').checked = currentSettings.autoUpdate;

    // 更新主题选择
    document.getElementById('themeSelect').value = currentSettings.theme;

    // 更新更新通道选择
    document.getElementById('updateChannelSelect').value = currentSettings.updateChannel || 'github';
    document.getElementById('customUpdateUrl').value = currentSettings.customUpdateUrl || '';

    // 显示/隐藏自定义URL设置
    toggleCustomUrlSetting();
}

// 保存设置
async function saveSettings() {
    try {
        await ipcRenderer.invoke('save-all-settings', currentSettings);
        showStatus(getNestedValue(i18nTexts, 'settings.messages.saved') || '设置已保存', 'success');
    } catch (error) {
        console.error('保存设置失败:', error);
        showStatus('保存设置失败: ' + error.message, 'danger');
    }
}

// 重置设置
async function resetSettings() {
    if (confirm(getNestedValue(i18nTexts, 'settings.messages.reset') || '确定要重置所有设置为默认值吗？')) {
        currentSettings = { ...DEFAULT_SETTINGS };
        updateUI();

        // 重新加载默认语言文本
        i18nTexts = await getDefaultI18nTexts(currentSettings.language);
        updateI18nTexts();

        showStatus(getNestedValue(i18nTexts, 'settings.messages.reset') || '设置已重置为默认值', 'info');
    }
}

// 返回主界面
async function goBack() {
    try {
        await ipcRenderer.invoke('switch-to-main');
    } catch (error) {
        console.error('返回主界面失败:', error);
    }
}

// 退出登录
async function logout() {
    const confirmText = getNestedValue(i18nTexts, 'main.logout.confirm') || '确定要退出登录吗？';
    if (confirm(confirmText)) {
        try {
            const result = await ipcRenderer.invoke('logout');
            if (result.success) {
                showStatus(getNestedValue(i18nTexts, 'main.status.logged_out') || '已退出登录', 'success');
            } else {
                showStatus(result.error, 'danger');
            }
        } catch (error) {
            console.error('退出登录失败:', error);
            showStatus('退出登录失败: ' + error.message, 'danger');
        }
    }
}

// 显示状态消息
function showStatus(message, type = 'success') {
    const alertElement = document.getElementById('statusAlert');
    const messageElement = document.getElementById('statusMessage');
    
    // 更新消息内容
    messageElement.textContent = message;
    
    // 更新样式
    alertElement.className = `alert alert-${type} mt-3`;
    alertElement.style.display = 'block';
    
    // 3秒后自动隐藏
    setTimeout(() => {
        alertElement.style.display = 'none';
    }, 3000);
}

// 切换自定义URL设置的显示/隐藏
function toggleCustomUrlSetting() {
    const customUrlSetting = document.getElementById('customUrlSetting');
    const updateChannel = document.getElementById('updateChannelSelect').value;

    if (updateChannel === 'custom') {
        customUrlSetting.style.display = 'block';
    } else {
        customUrlSetting.style.display = 'none';
    }
}

// 检查更新
async function checkForUpdates() {
    const checkBtn = document.getElementById('checkUpdateBtn');
    const statusDiv = document.getElementById('updateStatus');
    const statusMessage = document.getElementById('updateStatusMessage');

    // 禁用按钮并显示加载状态
    checkBtn.disabled = true;
    checkBtn.innerHTML = '<i class="bi bi-arrow-repeat me-1 spin"></i><span data-i18n="settings.update.checking">检查中...</span>';

    statusDiv.style.display = 'block';
    statusMessage.textContent = getNestedValue(i18nTexts, 'settings.update.checking_message') || '正在检查更新...';

    try {
        const result = await ipcRenderer.invoke('check-for-updates-with-channel', {
            channel: currentSettings.updateChannel,
            customUrl: currentSettings.customUpdateUrl
        });

        if (result.success) {
            if (result.hasUpdate) {
                statusDiv.className = 'mt-3';
                statusDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle me-1"></i>
                        <span>${getNestedValue(i18nTexts, 'settings.update.available') || '发现新版本'}: ${result.version}</span>
                    </div>
                `;
            } else {
                statusDiv.className = 'mt-3';
                statusDiv.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-1"></i>
                        <span>${getNestedValue(i18nTexts, 'settings.update.latest') || '当前已是最新版本'}</span>
                    </div>
                `;
            }
        } else {
            statusDiv.className = 'mt-3';
            statusDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-1"></i>
                    <span>${result.error || (getNestedValue(i18nTexts, 'settings.update.failed') || '检查更新失败')}</span>
                </div>
            `;
        }
    } catch (error) {
        console.error('检查更新失败:', error);
        statusDiv.className = 'mt-3';
        statusDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-x-circle me-1"></i>
                <span>${getNestedValue(i18nTexts, 'settings.update.error') || '检查更新时发生错误'}: ${error.message}</span>
            </div>
        `;
    } finally {
        // 恢复按钮状态
        checkBtn.disabled = false;
        checkBtn.innerHTML = '<i class="bi bi-arrow-repeat me-1"></i><span data-i18n="settings.update.check_button">检查更新</span>';

        // 3秒后隐藏状态信息
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 5000);
    }
}

// 监听键盘快捷键
document.addEventListener('keydown', (event) => {
    // Ctrl+S 或 Cmd+S 保存设置
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        saveSettings();
    }

    // Esc 返回
    if (event.key === 'Escape') {
        event.preventDefault();
        goBack();
    }
});
