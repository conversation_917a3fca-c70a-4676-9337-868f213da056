<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>AI重器 - 授权验证</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' http: https:;" id="csp-meta">
    <link href="../assets/styles/themes.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            padding: 40px;
            width: 400px;
            text-align: center;
        }

        .logo {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .title {
            font-size: 1.5em;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            opacity: 0.9;
        }

        input[type="text"] {
            width: 100%;
            padding: 12px 16px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 1em;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-sizing: border-box;
        }

        input[type="text"]::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        input[type="text"]:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            font-size: 1.1em;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            margin-top: 10px;
        }

        .login-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid rgba(255, 0, 0, 0.5);
            border-radius: 8px;
            padding: 10px;
            margin-top: 15px;
            color: #ffcccc;
            display: none;
        }

        .loading {
            display: none;
            margin-top: 15px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 2px solid white;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .help-text {
            margin-top: 20px;
            font-size: 0.9em;
            opacity: 0.7;
            line-height: 1.4;
        }

        .demo-codes {
            margin-top: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            font-size: 0.85em;
        }

        .demo-codes h4 {
            margin: 0 0 10px 0;
            font-size: 0.9em;
        }

        .demo-codes p {
            margin: 5px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🔐</div>
        <div class="title" data-i18n="login.title">AI重器 - 授权验证</div>

        <form id="loginForm">
            <div class="form-group">
                <label for="authCode" data-i18n="login.auth_code">授权码</label>
                <input type="text" id="authCode" data-i18n-placeholder="login.placeholder" placeholder="请输入您的授权码" value="ADMIN2024" required>
            </div>

            <button type="submit" class="login-btn" id="loginBtn" data-i18n="login.verify">
                验证授权码
            </button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <span data-i18n="login.verifying">正在验证授权码...</span>
        </div>

        <div class="error-message" id="errorMessage"></div>

        <div class="help-text">
            <span data-i18n="login.help_text">请输入有效的授权码以访问AI重器应用。</span>
            <div class="demo-codes">
                <h4 data-i18n="login.demo_codes">演示授权码：</h4>
                <p data-i18n="login.admin_code">管理员: ADMIN2024</p>
                <p data-i18n="login.user_code">普通用户: RDKKQF76AVX6WP4E</p>
            </div>
        </div>
    </div>

    <script src="login.js"></script>
</body>
</html>
