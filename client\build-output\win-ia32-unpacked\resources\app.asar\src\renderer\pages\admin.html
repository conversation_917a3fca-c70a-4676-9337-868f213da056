<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="admin.title">AI重器 - 管理面板</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' http: https: https://cdn.jsdelivr.net; img-src 'self' data: blob: http: https: https://cdn.jsdelivr.net;" id="csp-meta">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/styles/themes.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        .brand-icon, .user-avatar {
            width: 50px;
            height: 50px;
            object-fit: contain;
            border-radius: 8px;
        }
        .brand-icon-preview, .user-avatar-preview {
            width: 100px;
            height: 100px;
            object-fit: contain;
            border-radius: 8px;
            border: 2px dashed #dee2e6;
        }
        .loading {
            display: none;
        }
        .loading.show {
            display: block;
        }
        .sortable {
            cursor: move;
        }
        .sortable:hover {
            background-color: #f8f9fa;
        }
        .nav-tabs .nav-link {
            color: #495057;
        }
        .nav-tabs .nav-link.active {
            color: #0d6efd;
            font-weight: 500;
        }
        .user-type-badge {
            font-size: 0.75em;
        }
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            border-radius: 50px;
        }

        /* 文件预览样式 */
        .file-preview-container {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-preview-wrapper {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .file-thumbnail {
            width: 45px;
            height: 45px;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
            cursor: pointer;
        }

        .file-thumbnail:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .preview-fallback {
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            font-size: 1.5rem;
            color: #6c757d;
        }

        .file-icon-preview {
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            font-size: 1.8rem;
            transition: background-color 0.2s ease;
            cursor: pointer;
        }

        .file-icon-preview:hover {
            background-color: #e9ecef;
        }

        /* 文件名显示优化 */
        .file-name {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-weight: 500;
        }

        /* 文件类型徽章样式 */
        .badge {
            font-size: 0.75rem;
            padding: 0.35em 0.65em;
        }

        /* 表格行悬停效果 */
        .table-hover tbody tr:hover {
            background-color: rgba(0,0,0,.02);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .file-preview-container {
                width: 40px;
                height: 40px;
            }

            .file-thumbnail,
            .preview-fallback,
            .file-icon-preview {
                width: 35px;
                height: 35px;
                font-size: 1.2rem;
            }

            .file-name {
                max-width: 150px;
            }
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <button type="button" class="btn btn-secondary back-btn" onclick="goBack()">
        <i class="bi bi-arrow-left me-1"></i><span data-i18n="common.back">返回主界面</span>
    </button>

    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-gear-fill me-2"></i><span data-i18n="admin.title">AI重器 - 管理面板</span>
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="userInfo">
                    <i class="bi bi-person-circle me-1"></i><span data-i18n="app.loading">加载中...</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 标签页导航 -->
        <ul class="nav nav-tabs mb-4" id="adminTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="brands-tab" data-bs-toggle="tab" data-bs-target="#brands" type="button" role="tab">
                    <i class="bi bi-tags-fill me-2"></i><span data-i18n="admin.brand_management">品牌管理</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                    <i class="bi bi-people-fill me-2"></i><span data-i18n="admin.user_management">用户管理</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="files-tab" data-bs-toggle="tab" data-bs-target="#files" type="button" role="tab">
                    <i class="bi bi-files me-2"></i><span data-i18n="admin.file_management">文件管理</span>
                </button>
            </li>
        </ul>

        <!-- 标签页内容 -->
        <div class="tab-content" id="adminTabContent">
            <!-- 品牌管理标签页 -->
            <div class="tab-pane fade show active" id="brands" role="tabpanel">
                <div class="row mb-4">
                    <div class="col">
                        <div class="d-flex justify-content-between align-items-center">
                            <h2><i class="bi bi-tags-fill me-2"></i><span data-i18n="admin.brand_management">品牌管理</span></h2>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#brandModal" onclick="openAddBrandModal()">
                                <i class="bi bi-plus-lg me-1"></i><span data-i18n="admin.add_brand">添加品牌</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 品牌列表 -->
                <div class="row">
                    <div class="col">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-list-ul me-2"></i><span data-i18n="admin.brand_list">品牌列表</span>
                                    <span id="brandCount" class="badge bg-secondary ms-2">0</span>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="brandList" class="table-responsive">
                                    <!-- 品牌列表将在这里动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户管理标签页 -->
            <div class="tab-pane fade" id="users" role="tabpanel">
                <div class="row mb-4">
                    <div class="col">
                        <div class="d-flex justify-content-between align-items-center">
                            <h2><i class="bi bi-people-fill me-2"></i><span data-i18n="admin.user_management">用户管理</span></h2>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userModal" onclick="openAddUserModal()">
                                <i class="bi bi-plus-lg me-1"></i><span data-i18n="admin.add_user">添加用户</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 用户列表 -->
                <div class="row">
                    <div class="col">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-list-ul me-2"></i><span data-i18n="admin.user_list">用户列表</span>
                                    <span id="userCount" class="badge bg-secondary ms-2">0</span>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="userList" class="table-responsive">
                                    <!-- 用户列表将在这里动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文件管理标签页 -->
            <div class="tab-pane fade" id="files" role="tabpanel">
                <div class="row mb-4">
                    <div class="col">
                        <div class="d-flex justify-content-between align-items-center">
                            <h2><i class="bi bi-files me-2"></i><span data-i18n="admin.file_management">文件管理</span></h2>
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-secondary" onclick="refreshFileList()">
                                    <i class="bi bi-arrow-clockwise me-1"></i><span data-i18n="admin.file.actions.refresh">刷新</span>
                                </button>
                                <button type="button" class="btn btn-danger" onclick="cleanupFiles()" data-i18n-title="admin.file.messages.cleanup_confirm">
                                    <i class="bi bi-trash me-1"></i><span data-i18n="admin.file.actions.cleanup">清理</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary" data-i18n="admin.file.stats.total_files">总文件数</h5>
                                <h3 id="totalFiles" class="text-primary">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success" data-i18n="admin.file.stats.total_size">总大小</h5>
                                <h3 id="totalSize" class="text-success">0 MB</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info" data-i18n="admin.file.stats.image_files">图片文件</h5>
                                <h3 id="imageFiles" class="text-info">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning" data-i18n="admin.file.stats.other_files">其他文件</h5>
                                <h3 id="otherFiles" class="text-warning">0</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件过滤 -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <select class="form-select" id="fileTypeFilter" onchange="filterFiles()">
                            <option value="" data-i18n="admin.file.filters.all_types">所有类型</option>
                            <option value="image" data-i18n="admin.file.filters.image">图片</option>
                            <option value="document" data-i18n="admin.file.filters.document">文档</option>
                            <option value="video" data-i18n="admin.file.filters.video">视频</option>
                            <option value="audio" data-i18n="admin.file.filters.audio">音频</option>
                            <option value="other" data-i18n="admin.file.filters.other">其他</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="fileSearchInput" data-i18n-placeholder="admin.file.filters.search_placeholder" placeholder="搜索文件名..." onkeyup="searchFiles()">
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="fileSortOrder" onchange="sortFiles()">
                            <option value="newest" data-i18n="admin.file.filters.sort.newest">最新上传</option>
                            <option value="oldest" data-i18n="admin.file.filters.sort.oldest">最早上传</option>
                            <option value="largest" data-i18n="admin.file.filters.sort.largest">文件最大</option>
                            <option value="smallest" data-i18n="admin.file.filters.sort.smallest">文件最小</option>
                            <option value="name" data-i18n="admin.file.filters.sort.name">文件名</option>
                        </select>
                    </div>
                </div>

                <!-- 文件列表 -->
                <div class="row">
                    <div class="col">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-list-ul me-2"></i><span data-i18n="admin.file_list">文件列表</span>
                                    <span id="fileCount" class="badge bg-secondary ms-2">0</span>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="fileList" class="table-responsive">
                                    <!-- 文件列表将在这里动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载状态 -->
    <div id="loading" class="loading text-center position-fixed top-50 start-50 translate-middle">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2">正在加载数据...</p>
    </div>

    <!-- 品牌编辑模态框 -->
    <div class="modal fade" id="brandModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="brandModalTitle">添加品牌</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="brandForm">
                        <input type="hidden" id="brandId">
                        
                        <div class="mb-3">
                            <label for="brandName" class="form-label">品牌名称 *</label>
                            <input type="text" class="form-control" id="brandName" required maxlength="100">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">品牌图标 *</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="brandIcon" class="form-label">图标链接</label>
                                    <input type="url" class="form-control" id="brandIcon">
                                    <div class="form-text">输入图片URL或使用下方上传功能</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">或上传图标文件</label>
                                    <div id="brandIconUpload"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="brandDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="brandDescription" rows="3" maxlength="500"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="brandSortOrder" class="form-label">排序</label>
                            <input type="number" class="form-control" id="brandSortOrder" value="0">
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="brandIsActive" checked>
                            <label class="form-check-label" for="brandIsActive">启用状态</label>
                        </div>
                        
                        <!-- 图标预览 -->
                        <div class="mb-3">
                            <label class="form-label">图标预览</label>
                            <div class="text-center">
                                <img id="brandIconPreview" class="brand-icon-preview" src="" alt="图标预览" style="display: none;">
                                <div id="brandIconPlaceholder" class="brand-icon-preview d-inline-flex align-items-center justify-content-center">
                                    <i class="bi bi-image text-muted"></i>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveBrand()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户编辑模态框 -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalTitle">添加用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="userId">
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名 *</label>
                            <input type="text" class="form-control" id="username" required maxlength="50">
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">邮箱 *</label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="authCode" class="form-label">授权码 *</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="authCode" required maxlength="100">
                                <button type="button" class="btn btn-outline-secondary" onclick="generateAuthCode()">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            </div>
                            <div class="form-text">用户登录时需要使用的授权码</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="displayName" class="form-label">显示名称</label>
                            <input type="text" class="form-control" id="displayName" maxlength="100">
                        </div>
                        
                        <div class="mb-3">
                            <label for="userType" class="form-label">用户类型</label>
                            <select class="form-select" id="userType">
                                <option value="user">普通用户</option>
                                <option value="admin">管理员</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="userDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="userDescription" rows="3" maxlength="500"></textarea>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="userIsActive" checked>
                            <label class="form-check-label" for="userIsActive">启用状态</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p id="deleteMessage">确定要删除吗？</p>
                    <p class="text-danger">此操作不可恢复！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">删除</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../components/FileUpload.js"></script>
    <script src="admin.js"></script>
</body>
</html>
