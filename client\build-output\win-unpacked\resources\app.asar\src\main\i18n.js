/**
 * 主进程国际化模块
 * 处理主进程的多语言支持
 */

const fs = require('fs');
const path = require('path');

class I18n {
  constructor() {
    this.currentLanguage = 'zh-CN';
    this.texts = {};
    this.fallbackTexts = {};
    
    // 初始化
    this.init();
  }

  /**
   * 初始化国际化
   */
  init() {
    try {
      // 加载默认语言（中文）
      this.loadLanguage('zh-CN');
      this.fallbackTexts = { ...this.texts };
      
      console.log('国际化初始化完成，当前语言:', this.currentLanguage);
    } catch (error) {
      console.error('国际化初始化失败:', error);
    }
  }

  /**
   * 加载语言文件
   * @param {string} language - 语言代码
   */
  loadLanguage(language) {
    try {
      const languageFile = path.join(__dirname, '../renderer/assets/locales', `${language}.json`);
      
      if (fs.existsSync(languageFile)) {
        const content = fs.readFileSync(languageFile, 'utf8');
        this.texts = JSON.parse(content);
        this.currentLanguage = language;
        console.log('语言文件已加载:', language);
      } else {
        console.warn(`语言文件不存在: ${languageFile}`);
      }
    } catch (error) {
      console.error(`加载语言文件失败: ${language}`, error);
    }
  }

  /**
   * 切换语言
   * @param {string} language - 新语言代码
   */
  switchLanguage(language) {
    if (language !== this.currentLanguage) {
      this.loadLanguage(language);
    }
  }

  /**
   * 获取翻译文本
   * @param {string} key - 文本键
   * @param {object} params - 参数对象
   * @returns {string} 翻译后的文本
   */
  t(key, params = {}) {
    let text = this.getNestedValue(this.texts, key);
    
    // 如果当前语言没有找到，尝试使用回退语言
    if (!text) {
      text = this.getNestedValue(this.fallbackTexts, key);
    }
    
    // 如果还是没有找到，返回键名
    if (!text) {
      console.warn(`翻译文本未找到: ${key}`);
      return key;
    }

    // 参数替换
    return this.formatText(text, params);
  }

  /**
   * 获取嵌套对象的值
   * @param {object} obj - 对象
   * @param {string} key - 嵌套键
   * @returns {any} 值
   */
  getNestedValue(obj, key) {
    if (!obj || !key) return undefined;
    
    return key.split('.').reduce((current, keyPart) => {
      return current && current[keyPart] !== undefined ? current[keyPart] : undefined;
    }, obj);
  }

  /**
   * 格式化文本（参数替换）
   * @param {string} template - 模板文本
   * @param {object} params - 参数对象
   * @returns {string} 格式化后的文本
   */
  formatText(template, params = {}) {
    if (!template) return '';
    
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key] !== undefined ? params[key] : match;
    });
  }

  /**
   * 获取当前语言
   * @returns {string} 当前语言代码
   */
  getCurrentLanguage() {
    return this.currentLanguage;
  }

  /**
   * 获取所有文本
   * @returns {object} 所有翻译文本
   */
  getAllTexts() {
    return this.texts;
  }

  /**
   * 检查语言是否支持
   * @param {string} language - 语言代码
   * @returns {boolean} 是否支持
   */
  isLanguageSupported(language) {
    const supportedLanguages = ['zh-CN', 'en-US'];
    return supportedLanguages.includes(language);
  }

  /**
   * 获取支持的语言列表
   * @returns {Array} 支持的语言列表
   */
  getSupportedLanguages() {
    return [
      { code: 'zh-CN', name: '中文 (简体)' },
      { code: 'en-US', name: 'English' }
    ];
  }

  /**
   * 获取默认语言文本（用于渲染进程）
   * @param {string} language - 语言代码
   * @returns {object} 语言文本
   */
  getDefaultTexts(language = this.currentLanguage) {
    if (language !== this.currentLanguage) {
      // 临时加载指定语言
      const tempTexts = {};
      try {
        const languageFile = path.join(__dirname, '../renderer/assets/locales', `${language}.json`);
        if (fs.existsSync(languageFile)) {
          const content = fs.readFileSync(languageFile, 'utf8');
          return JSON.parse(content);
        }
      } catch (error) {
        console.error(`获取语言文本失败: ${language}`, error);
      }
    }
    
    return this.texts;
  }
}

// 创建单例实例
const i18n = new I18n();

module.exports = i18n;
