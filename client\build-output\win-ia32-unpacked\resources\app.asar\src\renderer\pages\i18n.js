const fs = require('fs');
const path = require('path');

/**
 * 国际化工具类
 * 提供多语言支持功能
 */
class I18n {
    constructor() {
        this.currentLanguage = 'zh-CN';
        this.texts = {};
        this.fallbackLanguage = 'zh-CN';
        this.localesPath = path.join(__dirname, 'locales');
    }

    /**
     * 初始化国际化
     * @param {string} language - 语言代码
     */
    async init(language = 'zh-CN') {
        this.currentLanguage = language;
        await this.loadLanguage(language);
        
        // 如果不是回退语言，也加载回退语言作为备用
        if (language !== this.fallbackLanguage) {
            await this.loadLanguage(this.fallbackLanguage, false);
        }
    }

    /**
     * 加载语言文件
     * @param {string} language - 语言代码
     * @param {boolean} setCurrent - 是否设置为当前语言
     */
    async loadLanguage(language, setCurrent = true) {
        try {
            const languageFile = path.join(this.localesPath, `${language}.json`);
            
            if (fs.existsSync(languageFile)) {
                const content = fs.readFileSync(languageFile, 'utf8');
                const languageTexts = JSON.parse(content);
                
                if (setCurrent) {
                    this.texts = languageTexts;
                    this.currentLanguage = language;
                } else {
                    // 合并到现有文本中作为回退
                    this.texts = this.mergeTexts(languageTexts, this.texts);
                }
                
                console.log(`语言文件已加载: ${language}`);
                return true;
            } else {
                console.warn(`语言文件不存在: ${languageFile}`);
                return false;
            }
        } catch (error) {
            console.error(`加载语言文件失败 (${language}):`, error);
            return false;
        }
    }

    /**
     * 合并文本对象
     * @param {object} fallback - 回退文本
     * @param {object} current - 当前文本
     */
    mergeTexts(fallback, current) {
        const merged = { ...fallback };
        
        function deepMerge(target, source) {
            for (const key in source) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    target[key] = target[key] || {};
                    deepMerge(target[key], source[key]);
                } else {
                    target[key] = source[key];
                }
            }
        }
        
        deepMerge(merged, current);
        return merged;
    }

    /**
     * 获取翻译文本
     * @param {string} key - 文本键，支持点号分隔的嵌套键
     * @param {object} params - 参数对象，用于替换文本中的占位符
     * @returns {string} 翻译后的文本
     */
    t(key, params = {}) {
        let text = this.getNestedValue(this.texts, key);
        
        if (text === undefined || text === null) {
            console.warn(`翻译文本未找到: ${key}`);
            return key; // 返回键名作为回退
        }

        // 替换参数
        if (typeof text === 'string' && Object.keys(params).length > 0) {
            text = this.replaceParams(text, params);
        }

        return text;
    }

    /**
     * 获取嵌套对象的值
     * @param {object} obj - 对象
     * @param {string} key - 键路径
     */
    getNestedValue(obj, key) {
        return key.split('.').reduce((current, keyPart) => {
            return current && current[keyPart] !== undefined ? current[keyPart] : undefined;
        }, obj);
    }

    /**
     * 替换文本中的参数占位符
     * @param {string} text - 原始文本
     * @param {object} params - 参数对象
     */
    replaceParams(text, params) {
        return text.replace(/\{(\w+)\}/g, (match, key) => {
            return params[key] !== undefined ? params[key] : match;
        });
    }

    /**
     * 获取当前语言
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * 获取所有可用语言
     */
    getAvailableLanguages() {
        try {
            const files = fs.readdirSync(this.localesPath);
            return files
                .filter(file => file.endsWith('.json'))
                .map(file => file.replace('.json', ''));
        } catch (error) {
            console.error('获取可用语言失败:', error);
            return ['zh-CN', 'en-US'];
        }
    }

    /**
     * 切换语言
     * @param {string} language - 新语言代码
     */
    async switchLanguage(language) {
        if (language !== this.currentLanguage) {
            await this.init(language);
            return true;
        }
        return false;
    }

    /**
     * 获取所有文本（用于前端）
     */
    getAllTexts() {
        return this.texts;
    }

    /**
     * 检查语言是否可用
     * @param {string} language - 语言代码
     */
    isLanguageAvailable(language) {
        const availableLanguages = this.getAvailableLanguages();
        return availableLanguages.includes(language);
    }

    /**
     * 获取语言显示名称
     * @param {string} language - 语言代码
     */
    getLanguageDisplayName(language) {
        const displayNames = {
            'zh-CN': '中文 (简体)',
            'en-US': 'English',
            'zh-TW': '中文 (繁體)',
            'ja-JP': '日本語',
            'ko-KR': '한국어',
            'fr-FR': 'Français',
            'de-DE': 'Deutsch',
            'es-ES': 'Español',
            'pt-BR': 'Português (Brasil)',
            'ru-RU': 'Русский'
        };
        
        return displayNames[language] || language;
    }

    /**
     * 格式化日期
     * @param {Date} date - 日期对象
     * @param {object} options - 格式化选项
     */
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        const formatOptions = { ...defaultOptions, ...options };
        
        try {
            return new Intl.DateTimeFormat(this.currentLanguage, formatOptions).format(date);
        } catch (error) {
            console.error('日期格式化失败:', error);
            return date.toLocaleDateString();
        }
    }

    /**
     * 格式化数字
     * @param {number} number - 数字
     * @param {object} options - 格式化选项
     */
    formatNumber(number, options = {}) {
        try {
            return new Intl.NumberFormat(this.currentLanguage, options).format(number);
        } catch (error) {
            console.error('数字格式化失败:', error);
            return number.toString();
        }
    }

    /**
     * 格式化货币
     * @param {number} amount - 金额
     * @param {string} currency - 货币代码
     * @param {object} options - 格式化选项
     */
    formatCurrency(amount, currency = 'CNY', options = {}) {
        const defaultOptions = {
            style: 'currency',
            currency: currency
        };
        
        const formatOptions = { ...defaultOptions, ...options };
        
        try {
            return new Intl.NumberFormat(this.currentLanguage, formatOptions).format(amount);
        } catch (error) {
            console.error('货币格式化失败:', error);
            return `${currency} ${amount}`;
        }
    }
}

// 创建全局实例
const i18n = new I18n();

module.exports = i18n;
