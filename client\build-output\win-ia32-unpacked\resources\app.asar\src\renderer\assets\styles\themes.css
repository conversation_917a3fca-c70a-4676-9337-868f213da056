/* 主题样式文件 */

/* 浅色主题（默认） */
:root {
    --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-secondary: #ffffff;
    --bg-tertiary: rgba(255, 255, 255, 0.95);
    --bg-card: rgba(255, 255, 255, 0.5);
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-muted: #999999;
    --border-color: rgba(0, 0, 0, 0.1);
    --shadow-color: rgba(31, 38, 135, 0.37);
    --input-bg: rgba(255, 255, 255, 0.8);
    --button-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --button-hover: rgba(255, 255, 255, 0.3);
    --alert-bg: rgba(255, 255, 255, 0.9);
}

/* 深色主题 */
[data-theme="dark"] {
    --bg-primary: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --bg-secondary: #1a1a1a;
    --bg-tertiary: rgba(30, 30, 30, 0.95);
    --bg-card: rgba(40, 40, 40, 0.8);
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --border-color: rgba(255, 255, 255, 0.2);
    --shadow-color: rgba(0, 0, 0, 0.5);
    --input-bg: rgba(40, 40, 40, 0.8);
    --button-primary: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    --button-hover: rgba(255, 255, 255, 0.1);
    --alert-bg: rgba(40, 40, 40, 0.9);
}

/* 自动主题（跟随系统） */
@media (prefers-color-scheme: dark) {
    [data-theme="auto"] {
        --bg-primary: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        --bg-secondary: #1a1a1a;
        --bg-tertiary: rgba(30, 30, 30, 0.95);
        --bg-card: rgba(40, 40, 40, 0.8);
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --text-muted: #888888;
        --border-color: rgba(255, 255, 255, 0.2);
        --shadow-color: rgba(0, 0, 0, 0.5);
        --input-bg: rgba(40, 40, 40, 0.8);
        --button-primary: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        --button-hover: rgba(255, 255, 255, 0.1);
        --alert-bg: rgba(40, 40, 40, 0.9);
    }
}

/* 应用主题变量到各个元素 */
body {
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.container, .settings-container, .login-container {
    background: var(--bg-primary);
}

.card, .settings-card, .login-card {
    background: var(--bg-tertiary) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 8px 32px 0 var(--shadow-color) !important;
}

.setting-section {
    background: var(--bg-card) !important;
    border: 1px solid var(--border-color) !important;
}

.form-control, .form-select {
    background: var(--input-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

.form-control:focus, .form-select:focus {
    background: var(--input-bg) !important;
    border-color: #667eea !important;
    color: var(--text-primary) !important;
}

.btn-primary {
    background: var(--button-primary) !important;
    border: none !important;
}

.btn-primary:hover {
    background: var(--button-primary) !important;
    filter: brightness(1.1);
}

.btn-outline-primary {
    border-color: #667eea !important;
    color: #667eea !important;
}

.btn-outline-primary:hover {
    background: #667eea !important;
    border-color: #667eea !important;
}

.alert {
    background: var(--alert-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

.navbar {
    background: var(--bg-primary) !important;
}

.nav-tabs .nav-link {
    color: var(--text-secondary) !important;
}

.nav-tabs .nav-link.active {
    background: var(--bg-card) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

.table {
    color: var(--text-primary) !important;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background: var(--bg-card) !important;
}

.modal-content {
    background: var(--bg-tertiary) !important;
    border: 1px solid var(--border-color) !important;
}

.modal-header {
    border-bottom: 1px solid var(--border-color) !important;
}

.modal-footer {
    border-top: 1px solid var(--border-color) !important;
}

/* 特殊元素的主题适配 */
.version-info {
    background: var(--bg-card) !important;
    color: var(--text-primary) !important;
}

.language-preview {
    background: var(--bg-card) !important;
    color: var(--text-secondary) !important;
}

.back-btn {
    background: var(--button-hover) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

.back-btn:hover {
    background: var(--button-hover) !important;
    filter: brightness(1.2);
    color: var(--text-primary) !important;
}

/* 加载动画的主题适配 */
.spinner-border {
    color: #667eea !important;
}

/* 徽章的主题适配 */
.badge {
    background: var(--button-primary) !important;
}

/* 下拉菜单的主题适配 */
.dropdown-menu {
    background: var(--bg-tertiary) !important;
    border: 1px solid var(--border-color) !important;
}

.dropdown-item {
    color: var(--text-primary) !important;
}

.dropdown-item:hover {
    background: var(--bg-card) !important;
    color: var(--text-primary) !important;
}

/* 工具提示的主题适配 */
.tooltip-inner {
    background: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

/* 进度条的主题适配 */
.progress {
    background: var(--bg-card) !important;
}

.progress-bar {
    background: var(--button-primary) !important;
}

/* 分页的主题适配 */
.page-link {
    background: var(--bg-tertiary) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

.page-link:hover {
    background: var(--bg-card) !important;
    color: var(--text-primary) !important;
}

.page-item.active .page-link {
    background: var(--button-primary) !important;
    border-color: #667eea !important;
}

/* 主题切换动画 */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 滚动条的主题适配 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-card);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}
